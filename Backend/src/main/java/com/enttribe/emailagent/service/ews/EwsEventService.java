package com.enttribe.emailagent.service.ews;

import com.enttribe.emailagent.constant.EmailConstants;
import com.enttribe.emailagent.dao.EmailPreferencesDao;
import com.enttribe.emailagent.dao.EmailUserDao;
import com.enttribe.emailagent.dto.AvailabilityResponse;
import com.enttribe.emailagent.dto.AvailableSlots;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.GraphUserDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.OutOfOffice;
import com.enttribe.emailagent.dto.SetAutomaticRepliesRequest;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.service.EventService;
import com.enttribe.emailagent.service.graph.GraphIntegrationService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.ConverterUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EWSUtils;
import com.enttribe.emailagent.utils.EmailUtils;
import com.enttribe.emailagent.utils.TokenUtils;

import com.enttribe.emailagent.utils.ZoomClient;
import com.enttribe.emailagent.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.PropertySet;
import microsoft.exchange.webservices.data.core.enumeration.availability.AvailabilityData;
import microsoft.exchange.webservices.data.core.enumeration.availability.MeetingAttendeeType;
import microsoft.exchange.webservices.data.core.enumeration.property.WellKnownFolderName;
import microsoft.exchange.webservices.data.core.response.AttendeeAvailability;
import microsoft.exchange.webservices.data.core.service.folder.CalendarFolder;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.misc.availability.AttendeeInfo;
import microsoft.exchange.webservices.data.misc.availability.AvailabilityOptions;
import microsoft.exchange.webservices.data.misc.availability.GetUserAvailabilityResults;
import microsoft.exchange.webservices.data.misc.availability.TimeWindow;
import microsoft.exchange.webservices.data.property.complex.availability.CalendarEvent;
import microsoft.exchange.webservices.data.property.complex.availability.WorkingHours;
import microsoft.exchange.webservices.data.search.CalendarView;
import microsoft.exchange.webservices.data.search.FindItemsResults;
import microsoft.exchange.webservices.data.core.enumeration.availability.FreeBusyViewType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import microsoft.exchange.webservices.data.core.enumeration.service.ConflictResolutionMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsOrCancellationsMode;
import microsoft.exchange.webservices.data.property.complex.Attendee;
import microsoft.exchange.webservices.data.property.complex.ItemId;
import microsoft.exchange.webservices.data.property.complex.MessageBody;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.enttribe.emailagent.utils.CommonUtils.convertEventDtosToMeetings;
import static com.enttribe.emailagent.utils.CommonUtils.convertLocalTimeToDate;
import static com.enttribe.emailagent.utils.CommonUtils.convertStringToDate;
import static com.enttribe.emailagent.utils.CommonUtils.convertToOutOfOffice;
import static com.enttribe.emailagent.utils.CommonUtils.findUnavailableTimeSlots;
import static com.enttribe.emailagent.utils.CommonUtils.parseStartAndEndDates;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "email.platform", havingValue = "ews")
public class EwsEventService implements EventService {


    @Value("${org.domain.name}")
    private List<String> orgDomainName;

    @Value("${outsiders.events.lookup.allow}")
    private Boolean allowOutsiders;
    private final EmailUserDao emailUserDao;
    private final EmailPreferencesDao preferencesDao;
    private final UserContextHolder userContextHolder;
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private TokenUtils tokenUtils;

    /**
     * Gets calendar events v1.
     *
     * @param email         the email
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @return the calendar events v 1
     * @throws URISyntaxException the uri syntax exception
     */
    @Override
    public List<EventDto> getCalendarEventsV1(String email, String startDateTime, String endDateTime, String subject) {
        return getCalendarEvents(email, startDateTime, endDateTime, false);
    }

    @Override
    public Map<String, Object> updateEvent(String eventId, String email, String meetingRequestJson) {
        return Map.of();
    }

    @Override
    public Map<String, String> declineMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method declineMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.decline(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method declineMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, String> acceptMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method acceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.accept(true);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method acceptMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, String> tentativelyAccept(String meetingRequestId) {
        try {
            log.debug("Inside @method tentativelyAcceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Tentatively accept the meeting request
            appointment.acceptTentatively(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method tentativelyAcceptMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, Object>  scheduleEvent(String email, Map<String, Object> requestBody) {
        try {
            String subject = (String) requestBody.get("subject");
            String body = (String) requestBody.get("body");
            String meetingStartTime = (String) requestBody.get("meetingStartTime");
            String meetingEndTime = (String) requestBody.get("meetingEndTime");
            List<String> attendees = (List<String>) requestBody.get("requiredAttendees");
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            Appointment appointment = new Appointment(service);

            // Set appointment properties
            appointment.setSubject(subject);
            appointment.setBody(MessageBody.getMessageBodyFromText(body));

            Date startDate, endDate;
            if (meetingStartTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(meetingStartTime);
                endDate = DateUtils.parseDate(meetingEndTime);
            } else {
                String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);
                startDate = DateUtils.parseDateWithTimeZone(meetingStartTime, timeZone);
                endDate = DateUtils.parseDateWithTimeZone(meetingEndTime, timeZone);
            }
            appointment.setStart(startDate);
            appointment.setEnd(endDate);
            String location = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION)).orElse(null);
            String locationUrl = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION_URL)).orElse(null);
            log.info("location is {} and locationUrl is {}", location, locationUrl);
            //            appointment.setLocation("Conference Room");

            // Add required attendees
            attendees.forEach(attendee -> {
                try {
                    appointment.getRequiredAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
            String meetingType = (String) requestBody.get("meetingType");
            String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);

            if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
                try {
                    Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(email, requestBody.get(EmailConstants.MEETING_START_TIME).toString(), requestBody.get(EmailConstants.MEETING_END_TIME).toString(), requestBody.get("subject").toString(), requestBody.get("body").toString(), timeZone);

                    if (zoomResponse != null) {
                        String zoomBody = requestBody.get("body").toString();
                        body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                        log.debug("body is {}", body);
                        appointment.setBody(MessageBody.getMessageBodyFromText(body));
                    }
                } catch (Exception e) {
                    log.error("Error setting Zoom meeting url :{}", e.getMessage(),e);}
            }
            setOptionalAttendees(appointment, requestBody);
            appointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);
            log.debug("Meeting scheduled successfully.");
            return Map.of(EmailConstants.RESULT,EmailConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error inside @method scheduleEvent : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT,EmailConstants.FAILED);
        }
    }

    private void setOptionalAttendees(Appointment appointment, Map<String, Object> requestBody) {
        if (requestBody != null && requestBody.containsKey("optionalAttendees")) {
            List<String> optionals = (List<String>) requestBody.get("optionalAttendees");
            optionals.forEach(attendee -> {
                try {
                    appointment.getOptionalAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
        }
    }

    @Override
    public Map<String, String> rescheduleEvent(Map<String, String> requestBody) {
        log.debug("Inside @method rescheduleEvent. @param : requestMap -> {}", requestBody);
        String email = userContextHolder.getCurrentUser().getEmail();
        try {
            // Get the ExchangeService object for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            String eventId = requestBody.get("eventId");
            // Bind to the appointment using the appointmentId
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);

            // Update the appointment's start and end time
            String startTime = requestBody.get("startTime");
            String endTime = requestBody.get("endTime");
            String timeZone = requestBody.get("timeZone");
            if (timeZone == null) {
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
                timeZone = preferences.getTimeZone();
            }

            Date startDate = DateUtils.parseDateWithTimeZone(startTime, timeZone);
            Date endDate = DateUtils.parseDateWithTimeZone(endTime, timeZone);
            appointment.setStart(startDate);
            appointment.setEnd(endDate);

            // Add description or reason for rescheduling
            String description = requestBody.get("rescheduleReason");
            if (description != null && !description.isEmpty()) {
                appointment.setBody(MessageBody.getMessageBodyFromText(description));
            }
            // Save the changes to the appointment
            appointment.update(ConflictResolutionMode.AutoResolve, SendInvitationsOrCancellationsMode.SendToAllAndSaveCopy);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error while rescheduling event : {} ", e.getMessage(),e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public List<EventDto> getEventDetailsBySubjectAndTime(String email, String subject, String startDateTime, String endDateTime) {
        log.debug("Inside @method getEventDetailsBySubjectAndTime. @param: email -> {}, subject -> {}, startDateTime -> {}, endDateTime -> {}",
                  email, subject, startDateTime, endDateTime);

        try {
            if (!EmailUtils.checkDomain(email, orgDomainName)) {
                log.warn("Email domain not allowed: {}", email);
                return new ArrayList<>();
            }

            if (!allowOutsiders) {
                boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
                if (!userExists) {
                    log.warn("User not found or deleted: {}", email);
                    return new ArrayList<>();
                }
            }

            Date startDate = convertStringToDate(startDateTime);
            Date endDate = convertStringToDate(endDateTime);

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            CalendarFolder cf = CalendarFolder.bind(service, WellKnownFolderName.Calendar);
            CalendarView view = new CalendarView(startDate, endDate);
            view.setMaxItemsReturned(100);

            FindItemsResults<Appointment> response = cf.findAppointments(view);
            List<EventDto> events = new ArrayList<>();

            for (Appointment appointment : response.getItems()) {
                appointment.load(PropertySet.FirstClassProperties);

                // Filter by subject if provided
                if (subject != null && !subject.isEmpty() &&
                    appointment.getSubject() != null &&
                    appointment.getSubject().toLowerCase().contains(subject.toLowerCase())) {

                    if (!appointment.getIsCancelled()) {
                        EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                        events.add(eventDto);
                    }
                }
            }

            log.debug("Found {} events matching subject '{}' for email: {}", events.size(), subject, email);
            return events;

        } catch (Exception e) {
            log.error("Error inside @method getEventDetailsBySubjectAndTime: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EventDto> getEventDetails(String email, String eventId, String startDateTime, String endDateTime) {
        log.debug("Inside @method getEventDetails. @param: email -> {}, eventId -> {}, startDateTime -> {}, endDateTime -> {}",
                  email, eventId, startDateTime, endDateTime);

        try {
            if (!EmailUtils.checkDomain(email, orgDomainName)) {
                log.warn("Email domain not allowed: {}", email);
                return new ArrayList<>();
            }

            if (!allowOutsiders) {
                boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
                if (!userExists) {
                    log.warn("User not found or deleted: {}", email);
                    return new ArrayList<>();
                }
            }

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the specific appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            List<EventDto> events = new ArrayList<>();

            if (!appointment.getIsCancelled()) {
                // Check if the appointment falls within the specified date range
                Date startDate = convertStringToDate(startDateTime);
                Date endDate = convertStringToDate(endDateTime);

                if ((appointment.getStart().equals(startDate) || appointment.getStart().after(startDate)) &&
                    (appointment.getEnd().equals(endDate) || appointment.getEnd().before(endDate))) {

                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    events.add(eventDto);
                }
            }

            log.debug("Found {} event details for eventId: {}", events.size(), eventId);
            return events;

        } catch (Exception e) {
            log.error("Error inside @method getEventDetails: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public AvailableSlots getAvailableSlotsAndConflictV1(List<String> emails, String startDateTime, String endDateTime, int slotDuration, Boolean workingFlag) throws Exception {
        log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
        String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
        startDateTime = dateTimeRange[0];
        endDateTime = dateTimeRange[1];
        Map<String, List<Meeting>> result = fetchAndConvertCalendarEvents(emails, startDateTime, endDateTime);
        Date[] parsedDates = parseStartAndEndDates(startDateTime, endDateTime);
        Date start = parsedDates[0];
        Date end = parsedDates[1];
        long durationMillis = (long) slotDuration * 60 * 1000;
        AvailableSlots availableSlots = new AvailableSlots();
        availableSlots.setAvailableSlots(graphIntegrationService.findAvailableTimeSlots(result, start, end, durationMillis));
        availableSlots.setConflictMeeting(findUnavailableTimeSlots(result, start, end));
        availableSlots.setOutOfOffice(getOutOfOfficeDetails(result, start, end));
        return availableSlots;
    }

    private String[] setupDateTimeRange(String startDateTime, String endDateTime) {
        if (startDateTime == null || endDateTime == null) {
            String email = userContextHolder.getCurrentUser().getEmail();
            EmailUser user = emailUserDao.findByEmail(email);
            String userId = user.getUserId();
            EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
            startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
            endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
        }
        return new String[]{startDateTime, endDateTime};
    }

    private Map<String, List<Meeting>> fetchAndConvertCalendarEvents(List<String> emails, String startDateTime, String endDateTime) {
        Map<String, List<Meeting>> result = new HashMap<>();
        for (String email : emails) {
            List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
            if (calendarEvents == null || calendarEvents.isEmpty()) {
                log.debug("No calendar events found for email: {}", email);
                result.put(email, new ArrayList<>());
                continue;
            }
            List<Meeting> list = convertEventDtosToMeetings(calendarEvents);
            result.put(email, list);
        }
        return result;
    }

    public List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime, boolean forAvailability) {
        log.debug("Inside @method getCalendarEvents. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        List<EventDto> eventDtos = new ArrayList<>();
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) {
            log.warn("No user found for email: {}", email);
            return eventDtos; // Return an empty list
        }
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        String timeZone = preferences.getTimeZone();

        Date startDate;
        Date endDate;
        if (startDateTime == null || endDateTime == null) {
            startDate = DateUtils.getFormattedDateTime(false, timeZone);
            endDate = DateUtils.getFormattedDateTime(true, timeZone);
        } else {
            if (startDateTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(startDateTime);
                endDate = DateUtils.parseDate(endDateTime);
            } else {
                startDate = DateUtils.parseDateWithTimeZone(startDateTime, timeZone);
                endDate = DateUtils.parseDateWithTimeZone(endDateTime, timeZone);
            }
        }

        List<EventDto> events = new ArrayList<>();

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            CalendarFolder cf = CalendarFolder.bind(service, WellKnownFolderName.Calendar);
            CalendarView view = new CalendarView(startDate, endDate);
            view.setMaxItemsReturned(100);
            FindItemsResults<Appointment> response = cf.findAppointments(view);
            for (Appointment appointment : response.getItems()) {
                appointment.load(PropertySet.FirstClassProperties);
                if (!appointment.getIsCancelled() &&
                        !appointment.getRequiredAttendees().getItems().stream()
                                .allMatch(res -> res.getResponseType().toString().equals("Decline"))) {

                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    events.add(eventDto);
                }
            }
            return events;
        } catch (Exception e) {
            log.error("Error inside @method getCalendarEvents", e);
        }
        return new ArrayList<>();
    }


    private List<OutOfOffice> getOutOfOfficeDetails(Map<String, List<Meeting>> result, Date start, Date end) {
        List<OutOfOffice> outOfOffice = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"); // Assuming ISO 8601 format

        try {
            for (Map.Entry<String, List<Meeting>> entry : result.entrySet()) {
                String key = entry.getKey();
                //  List<Meeting> meetings = entry.getValue();
                Map<String, Object> userSettings = graphIntegrationService.getAutoReplySettingsForUser(key);
                if (userSettings != null) {
                    log.info("userSettings not null");
                    OutOfOffice office = convertToOutOfOffice(key, userSettings);
                    Date oooStart = null;
                    Date oooEnd = null;
                    if (office.getStartDateTime() != null && office.getEndDateTime() != null) {
                        oooStart = dateFormat.parse(office.getStartDateTime());
                        oooEnd = dateFormat.parse(office.getEndDateTime());
                    } else {
                        EmailPreferences emailPreferences = preferencesDao.getEmailPreferencesByUserId(key);
                        oooStart = convertLocalTimeToDate(emailPreferences.getCheckin());
                        oooEnd = convertLocalTimeToDate(emailPreferences.getCheckout());
                    }
                    if (oooStart != null && oooEnd != null && (oooStart.before(end) || oooStart.equals(end)) && (oooEnd.after(start) || oooEnd.equals(start)))
                        outOfOffice.add(office);
                }
            }
        } catch (Exception e) {
            log.error("error inside getAutoReplySettingsForUser", e);
        }
        return outOfOffice;
    }

    @Override
    public String getSchedule(List<String> emails, String startDateTime, String endDateTime, Integer slotDuration, Boolean workingFlag) {
        log.debug("Inside @method getSchedule. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}, slotDuration -> {}",
                  emails, startDateTime, endDateTime, slotDuration);

        try {
            // Parse dates
            Date startDate = convertStringToDate(startDateTime);
            Date endDate = convertStringToDate(endDateTime);

            // Create the response structure matching Graph API format
            Map<String, Object> response = new HashMap<>();
            response.put("@odata.context", "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.scheduleInformation)");

            List<Map<String, Object>> scheduleInfoList = new ArrayList<>();

            for (String email : emails) {
                try {
                    Map<String, Object> scheduleInfo = getScheduleInfoForUser(email, startDate, endDate);
                    if (scheduleInfo != null) {
                        scheduleInfoList.add(scheduleInfo);
                    }
                } catch (Exception e) {
                    log.error("Error getting schedule for user {}: {}", email, e.getMessage(), e);
                    // Continue with other users even if one fails
                }
            }

            response.put("value", scheduleInfoList);
            return JsonUtils.convertToJSON(response);

        } catch (Exception e) {
            log.error("Error in getSchedule: {}", e.getMessage(), e);
            return "{}";
        }
    }

    private Map<String, Object> getScheduleInfoForUser(String email, Date startDate, Date endDate) throws Exception {
        ExchangeService service = EWSUtils.getServiceObjectForUser(email);

        // Create attendee info for the user
        List<AttendeeInfo> attendees = new ArrayList<>();
        AttendeeInfo attendee = new AttendeeInfo();
        attendee.setSmtpAddress(email);
        attendee.setAttendeeType(MeetingAttendeeType.Required);
        attendees.add(attendee);

        // Set up availability options
        AvailabilityOptions availabilityOptions = new AvailabilityOptions();
        availabilityOptions.setRequestedFreeBusyView(FreeBusyViewType.FreeBusy);

        // Create time window
        TimeWindow timeWindow = new TimeWindow(startDate, endDate);

        // Get user availability
        GetUserAvailabilityResults results = service.getUserAvailability(
            attendees,
            timeWindow,
            AvailabilityData.FreeBusy,
            availabilityOptions
        );

        if (results.getAttendeesAvailability().getCount() == 0) {
            return new HashMap<>();
        }

        AttendeeAvailability availability = results.getAttendeesAvailability().getResponseAtIndex(0);

        // Build the schedule info response
        Map<String, Object> scheduleInfo = new HashMap<>();
        scheduleInfo.put("scheduleId", email);

        // Generate availability view (simplified version)
        String availabilityView = generateAvailabilityView(availability, startDate, endDate);
        scheduleInfo.put("availabilityView", availabilityView);

        // Add schedule items from calendar events
        List<Map<String, Object>> scheduleItems = convertCalendarEventsToScheduleItems(availability.getCalendarEvents());
        scheduleInfo.put("scheduleItems", scheduleItems);

        // Add working hours
        WorkingHours workingHours = availability.getWorkingHours();
        if (workingHours != null) {
            scheduleInfo.put("workingHours", convertWorkingHours(workingHours));
        } else {
            // Default working hours if not available
            scheduleInfo.put("workingHours", getDefaultWorkingHours());
        }

        return scheduleInfo;
    }

    private String generateAvailabilityView(AttendeeAvailability availability, Date startDate, Date endDate) {
        StringBuilder availabilityView = new StringBuilder();

        try {
            // Calculate time slots (30-minute intervals by default)
            long intervalMinutes = 30;
            long intervalMillis = intervalMinutes * 60 * 1000;

            Date currentTime = new Date(startDate.getTime());

            while (currentTime.before(endDate)) {
                Date slotEnd = new Date(currentTime.getTime() + intervalMillis);

                // Check if this time slot has any busy events
                boolean isBusy = false;
                for (CalendarEvent event : availability.getCalendarEvents()) {
                    if (event.getStartTime().before(slotEnd) && event.getEndTime().after(currentTime)) {
                        // There's an overlap, check busy type
                        switch (event.getFreeBusyStatus().toString()) {
                            case "Busy":
                                availabilityView.append("2");
                                isBusy = true;
                                break;
                            case "Tentative":
                                availabilityView.append("1");
                                isBusy = true;
                                break;
                            case "OutOfOffice":
                                availabilityView.append("3");
                                isBusy = true;
                                break;
                            case "WorkingElsewhere":
                                availabilityView.append("4");
                                isBusy = true;
                                break;
                            default:
                                break;
                        }
                        if (isBusy) break;
                    }
                }

                if (!isBusy) {
                    availabilityView.append("0"); // Free
                }

                currentTime = slotEnd;
            }
        } catch (Exception e) {
            log.error("Error generating availability view: {}", e.getMessage(), e);
            return "0"; // Default to free if error
        }

        return availabilityView.toString();
    }

    private List<Map<String, Object>> convertCalendarEventsToScheduleItems(Iterable<CalendarEvent> calendarEvents) {
        List<Map<String, Object>> scheduleItems = new ArrayList<>();

        try {
            SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");

            for (CalendarEvent event : calendarEvents) {
                // Only include busy events (not free time)
                if (!"Free".equals(event.getFreeBusyStatus().toString())) {
                    Map<String, Object> scheduleItem = new HashMap<>();

                    // Add start and end times
                    Map<String, Object> start = new HashMap<>();
                    start.put("dateTime", isoFormat.format(event.getStartTime()));
                    start.put("timeZone", "UTC");
                    scheduleItem.put("start", start);

                    Map<String, Object> end = new HashMap<>();
                    end.put("dateTime", isoFormat.format(event.getEndTime()));
                    end.put("timeZone", "UTC");
                    scheduleItem.put("end", end);

                    // Add status based on busy type
                    String status = mapBusyTypeToStatus(event.getFreeBusyStatus().toString());
                    scheduleItem.put("status", status);

                    // Add subject if available (may be null for free/busy only requests)
                    scheduleItem.put("subject", event.getDetails() != null ? event.getDetails().getSubject() : "");

                    scheduleItems.add(scheduleItem);
                }
            }
        } catch (Exception e) {
            log.error("Error converting calendar events to schedule items: {}", e.getMessage(), e);
        }

        return scheduleItems;
    }

    private String mapBusyTypeToStatus(String busyType) {
        switch (busyType) {
            case "Busy":
                return "busy";
            case "Tentative":
                return "tentative";
            case "OutOfOffice":
                return "oof";
            case "WorkingElsewhere":
                return "workingElsewhere";
            default:
                return "free";
        }
    }

    private Map<String, Object> convertWorkingHours(WorkingHours workingHours) {
        Map<String, Object> workingHoursMap = new HashMap<>();

        try {
            // Convert days of week
            List<String> daysOfWeek = new ArrayList<>();
            if (workingHours.getDaysOfTheWeek() != null) {
                String daysString = workingHours.getDaysOfTheWeek().toString();
                if (daysString.contains("Monday")) daysOfWeek.add("monday");
                if (daysString.contains("Tuesday")) daysOfWeek.add("tuesday");
                if (daysString.contains("Wednesday")) daysOfWeek.add("wednesday");
                if (daysString.contains("Thursday")) daysOfWeek.add("thursday");
                if (daysString.contains("Friday")) daysOfWeek.add("friday");
                if (daysString.contains("Saturday")) daysOfWeek.add("saturday");
                if (daysString.contains("Sunday")) daysOfWeek.add("sunday");
            }

            if (daysOfWeek.isEmpty()) {
                // Default to weekdays
                daysOfWeek.addAll(List.of("monday", "tuesday", "wednesday", "thursday", "friday"));
            }

            workingHoursMap.put("daysOfWeek", daysOfWeek);

            // Convert start and end times (default to 8 AM - 5 PM if not available)
            String startTime = "08:00:00.0000000";
            String endTime = "17:00:00.0000000";

            long startMinutesLong = workingHours.getStartTime();
            int startHours = (int) startMinutesLong / 60;
            int startMinutes = (int) startMinutesLong % 60;
            startTime = String.format("%02d:%02d:00.0000000", startHours, startMinutes);


            long endMinutesLong = workingHours.getEndTime(); // returns long, total startMinutes since midnight
            int endHours = (int) (endMinutesLong / 60);
            int endMinutes = (int) (endMinutesLong % 60);
            endTime = String.format("%02d:%02d:00.0000000", endHours, endMinutes);


            workingHoursMap.put("startTime", startTime);
            workingHoursMap.put("endTime", endTime);

            // Add timezone information
            Map<String, String> timeZone = new HashMap<>();
            timeZone.put("name", "Pacific Standard Time"); // Default timezone
            workingHoursMap.put("timeZone", timeZone);

        } catch (Exception e) {
            log.error("Error converting working hours: {}", e.getMessage(), e);
            return getDefaultWorkingHours();
        }

        return workingHoursMap;
    }

    private Map<String, Object> getDefaultWorkingHours() {
        Map<String, Object> defaultWorkingHours = new HashMap<>();
        defaultWorkingHours.put("daysOfWeek", List.of("monday", "tuesday", "wednesday", "thursday", "friday"));
        defaultWorkingHours.put("startTime", "08:00:00.0000000");
        defaultWorkingHours.put("endTime", "17:00:00.0000000");

        Map<String, String> timeZone = new HashMap<>();
        timeZone.put("name", "Pacific Standard Time");
        defaultWorkingHours.put("timeZone", timeZone);

        return defaultWorkingHours;
    }

    @Override
    public AvailabilityResponse getAvailability(List<String> emails, String startDateTime, String endDateTime, int slotDuration, Boolean workingFlag) throws Exception {
        return null;
    }

    @Override
    public Map<String, String> cancelEvent(String eventId, String comment) throws Exception {
        log.debug("Inside @method cancelEvent. @param: eventId -> {}, comment -> {}", eventId, comment);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            // Cancel the appointment with the provided comment
            if (comment != null && !comment.isEmpty()) {
                appointment.setBody(MessageBody.getMessageBodyFromText(comment));
            }

            // Cancel and send cancellation to all attendees
            appointment.cancelMeeting(comment);

            log.debug("Event cancelled successfully: {}", eventId);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);

        } catch (Exception e) {
            log.error("Error inside @method cancelEvent: {}", e.getMessage(), e);
            throw new Exception("Failed to cancel event: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, String> forwardEvent(String eventId, List<String> emailIds, String comment) throws IOException, InterruptedException {
        log.debug("Inside @method forwardEvent. @param: eventId -> {}, emailIds -> {}, comment -> {}", eventId, emailIds, comment);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            // Create a new appointment based on the original one
            Appointment forwardedAppointment = new Appointment(service);
            forwardedAppointment.setSubject("Fwd: " + appointment.getSubject());

            // Set body with forward comment
            String forwardBody = "";
            if (comment != null && !comment.isEmpty()) {
                forwardBody = comment + "\n\n--- Forwarded Event ---\n";
            } else {
                forwardBody = "--- Forwarded Event ---\n";
            }

            if (appointment.getBody() != null) {
                forwardBody += appointment.getBody().toString();
            }

            forwardedAppointment.setBody(MessageBody.getMessageBodyFromText(forwardBody));
            forwardedAppointment.setStart(appointment.getStart());
            forwardedAppointment.setEnd(appointment.getEnd());
            forwardedAppointment.setLocation(appointment.getLocation());

            // Add recipients as required attendees
            for (String emailId : emailIds) {
                forwardedAppointment.getRequiredAttendees().add(emailId);
            }

            // Save and send the forwarded appointment
            forwardedAppointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);

            log.debug("Event forwarded successfully to: {}", emailIds);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);

        } catch (Exception e) {
            log.error("Error inside @method forwardEvent: {}", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public EventDto getCalendarEventByEventId(String eventId) throws Exception {
        log.debug("Inside @method getCalendarEventByEventId. @param: eventId -> {}", eventId);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            if (email == null || email.isEmpty()) {
                throw new RuntimeException("User email not found in context");
            }

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            if (appointment.getIsCancelled()) {
                log.warn("Requested event is cancelled: {}", eventId);
                return null;
            }

            EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
            log.debug("Successfully retrieved event: {}", eventId);
            return eventDto;

        } catch (Exception e) {
            log.error("Error inside @method getCalendarEventByEventId: {}", e.getMessage(), e);
            throw new Exception("Failed to get calendar event by ID: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        try {
            log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
                EmailUser user = emailUserDao.findByEmail(email);
                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }
            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, new ArrayList<>());  // Add empty list for this email
                    continue;
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                result.put(email, list);
            }
            Date start, end;
            if (startDateTime.endsWith("Z")) {
                start = convertStringToDate(startDateTime);
                end = convertStringToDate(endDateTime);
            } else {
                start = DateUtils.parseDateWithoutTZ(startDateTime);
                end = DateUtils.parseDateWithoutTZ(endDateTime);
            }

            long durationMillis = slotDuration * 60 * 1000;
            return graphIntegrationService.findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            log.error("Error inside @method getAvailableSlot : {} ", e.getMessage(), e);}
        return null;
    }

    @Override
    public Map<String, Object> updateUserDetails(GraphUserDto graphUserDto) {
        log.debug("Inside @method updateUserDetails. @param: graphUserDto -> {}", graphUserDto);

        // EWS doesn't support user management operations like Graph API
        // This would typically require Active Directory operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("updateUserDetails is not supported in EWS implementation. User management should be done through Active Directory or other user management systems.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "User details update not supported in EWS implementation");
        result.put("reason", "EWS does not provide user management capabilities. Use Active Directory or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> createUser(Map<String, Object> userRequest) {
        log.debug("Inside @method createUser. @param: userRequest -> {}", userRequest);

        // EWS doesn't support user creation operations like Graph API
        // This would typically require Active Directory operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("createUser is not supported in EWS implementation. User creation should be done through Active Directory or other user management systems.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "User creation not supported in EWS implementation");
        result.put("reason", "EWS does not provide user management capabilities. Use Active Directory or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> assignLicenseToUser(String userPrincipalName) {
        log.debug("Inside @method assignLicenseToUser. @param: userPrincipalName -> {}", userPrincipalName);

        // EWS doesn't support license assignment operations like Graph API
        // This would typically require Microsoft 365 admin operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("assignLicenseToUser is not supported in EWS implementation. License assignment should be done through Microsoft 365 Admin Center or Graph API.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "License assignment not supported in EWS implementation");
        result.put("reason", "EWS does not provide license management capabilities. Use Microsoft 365 Admin Center or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> setAutomaticRepliesSettings(String userId, SetAutomaticRepliesRequest request) throws Exception {
        return Map.of();
    }

}
